services:
  agent-zero-cuda:
    container_name: agent-zero-cuda
    image: frdel/agent-zero-run-cuda:testing
    volumes:
      - ./agent-zero:/a0
      - ./agent-zero/work_dir:/root
    ports:
      - "50080:80"
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - PYTHONUNBUFFERED=1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu] 